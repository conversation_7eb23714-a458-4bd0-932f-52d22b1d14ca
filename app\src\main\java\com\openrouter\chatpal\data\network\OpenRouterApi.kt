package com.openrouter.chatpal.data.network

import com.openrouter.chatpal.data.model.OpenRouterRequest
import com.openrouter.chatpal.data.model.OpenRouterResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface OpenRouterApi {
    
    @POST("chat/completions")
    suspend fun sendMessage(
        @Header("Authorization") authorization: String,
        @Header("HTTP-Referer") referer: String,
        @Header("X-Title") title: String,
        @Body request: OpenRouterRequest
    ): Response<OpenRouterResponse>
}

object OpenRouterApiConstants {
    const val BASE_URL = "https://openrouter.ai/api/v1/"
    const val APP_TITLE = "OpenRouter Chat Pal Android"
    const val REFERER = "https://github.com/openrouter/chatpal-android"
    
    // Available models
    val AVAILABLE_MODELS = listOf(
        "openai/gpt-4.1" to "GPT-4.1",
        "openai/gpt-4o" to "GPT-4o", 
        "openai/gpt-4o-mini" to "GPT-4o Mini",
        "anthropic/claude-3.5-sonnet" to "Claude 3.5 Sonnet",
        "google/gemini-pro" to "Gemini Pro",
        "meta-llama/llama-3.1-8b-instruct" to "Llama 3.1 8B",
        "mistralai/mistral-7b-instruct" to "Mistral 7B"
    )
    
    // System prompt templates
    val PROMPT_TEMPLATES = mapOf(
        "Default" to "You are a helpful assistant.",
        "Creative Writer" to "You are a creative writing assistant. Help users craft engaging stories, poems, and creative content.",
        "Code Expert" to "You are an expert programmer. Provide clean, efficient code solutions and explain complex programming concepts.",
        "Academic Tutor" to "You are an academic tutor. Explain concepts clearly and help students understand complex topics.",
        "Business Advisor" to "You are a business consultant. Provide strategic advice and help solve business challenges."
    )
}

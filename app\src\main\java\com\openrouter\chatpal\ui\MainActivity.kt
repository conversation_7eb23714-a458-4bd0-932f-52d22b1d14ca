package com.openrouter.chatpal.ui

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.openrouter.chatpal.ui.chat.ChatScreen
import com.openrouter.chatpal.ui.chat.ChatViewModel
import com.openrouter.chatpal.ui.conversations.ConversationsActivity
import com.openrouter.chatpal.ui.settings.SettingsActivity
import com.openrouter.chatpal.ui.theme.ChatPalTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    private val chatViewModel: ChatViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check if we should load a specific conversation
        val conversationId = intent.getStringExtra("conversation_id")
        if (conversationId != null) {
            chatViewModel.loadConversation(conversationId)
        }

        setContent {
            ChatPalTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen(
                        chatViewModel = chatViewModel,
                        onNavigateToConversations = {
                            startActivity(Intent(this@MainActivity, ConversationsActivity::class.java))
                        },
                        onNavigateToSettings = {
                            startActivity(Intent(this@MainActivity, SettingsActivity::class.java))
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun MainScreen(
    chatViewModel: ChatViewModel,
    onNavigateToConversations: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    val context = LocalContext.current
    val uiState by chatViewModel.uiState.collectAsStateWithLifecycle()
    
    ChatScreen(
        uiState = uiState,
        onSendMessage = chatViewModel::sendMessage,
        onToggleMessageFavorite = chatViewModel::toggleMessageFavorite,
        onEditMessage = chatViewModel::editMessage,
        onRegenerateResponse = chatViewModel::regenerateResponse,
        onAddReaction = chatViewModel::addReaction,
        onCopyMessage = { content ->
            val clipboard = context.getSystemService(android.content.Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("Message", content)
            clipboard.setPrimaryClip(clip)
            Toast.makeText(context, "Copied to clipboard", Toast.LENGTH_SHORT).show()
        },
        onNavigateToConversations = onNavigateToConversations,
        onNavigateToSettings = onNavigateToSettings,
        onNewConversation = chatViewModel::startNewConversation,
        onClearChat = chatViewModel::clearCurrentChat,
        onSetApiKey = chatViewModel::setApiKey
    )

    // Handle error messages
    uiState.errorMessage?.let { error ->
        LaunchedEffect(error) {
            Toast.makeText(context, error, Toast.LENGTH_LONG).show()
            chatViewModel.dismissError()
        }
    }
}

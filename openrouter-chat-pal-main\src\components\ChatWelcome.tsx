
import React from 'react';
import { MessageSquare, <PERSON>, <PERSON>ting<PERSON>, Sparkles, Zap } from 'lucide-react';

interface ChatWelcomeProps {
  hasApiKey: boolean;
  onShowApiKeyInput: () => void;
}

const ChatWelcome: React.FC<ChatWelcomeProps> = ({
  hasApiKey,
  onShowApiKeyInput,
}) => {
  return (
    <div className="text-center py-8 sm:py-16 px-4">
      <div className="relative mb-6 sm:mb-8">
        <div className="w-16 h-16 sm:w-20 sm:h-20 gradient-primary rounded-2xl sm:rounded-3xl flex items-center justify-center mx-auto shadow-glow animate-glow">
          <MessageSquare className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
        </div>
        <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-6 h-6 sm:w-8 sm:h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
          <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-800" />
        </div>
      </div>
      <h2 className="text-2xl sm:text-4xl font-bold text-gradient mb-3 sm:mb-4">Welcome to Advanced AI Chat</h2>
      <p className="text-base sm:text-xl text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 max-w-2xl mx-auto">
        Experience the future of AI conversation with advanced features, beautiful design, and powerful capabilities
      </p>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 max-w-4xl mx-auto mb-6 sm:mb-8">
        <div className="card-modern p-4 sm:p-6 rounded-xl sm:rounded-2xl text-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-primary rounded-lg sm:rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4">
            <Zap className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Lightning Fast</h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">Get instant responses with advanced AI models</p>
        </div>
        
        <div className="card-modern p-4 sm:p-6 rounded-xl sm:rounded-2xl text-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-primary rounded-lg sm:rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4">
            <Settings className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Highly Customizable</h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">Fine-tune AI behavior with advanced settings</p>
        </div>
        
        <div className="card-modern p-4 sm:p-6 rounded-xl sm:rounded-2xl text-center sm:col-span-2 lg:col-span-1">
          <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-primary rounded-lg sm:rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4">
            <MessageSquare className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Smart Conversations</h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">Manage and organize your chat history</p>
        </div>
      </div>
      
      {!hasApiKey && (
        <button
          onClick={onShowApiKeyInput}
          className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 gradient-primary text-white rounded-xl sm:rounded-2xl hover:shadow-glow transition-all duration-300 font-semibold text-base sm:text-lg btn-modern"
        >
          <Key className="w-4 h-4 sm:w-5 sm:h-5" />
          Get Started - Set API Key
        </button>
      )}
    </div>
  );
};

export default ChatWelcome;

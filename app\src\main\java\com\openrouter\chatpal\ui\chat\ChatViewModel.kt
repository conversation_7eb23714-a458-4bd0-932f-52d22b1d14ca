package com.openrouter.chatpal.ui.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.openrouter.chatpal.data.model.*
import com.openrouter.chatpal.data.repository.ChatRepository
import com.openrouter.chatpal.utils.PreferencesManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class ChatViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    private val preferencesManager: PreferencesManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ChatUiState())
    val uiState: StateFlow<ChatUiState> = _uiState.asStateFlow()
    
    private var currentConversationId: String? = null
    
    init {
        loadApiKey()
        loadSettings()
        startNewConversation()
    }
    
    private fun loadApiKey() {
        val apiKey = preferencesManager.getApiKey()
        _uiState.update { it.copy(apiKey = apiKey) }
    }
    
    private fun loadSettings() {
        val settings = preferencesManager.getChatSettings()
        _uiState.update { it.copy(settings = settings) }
    }
    
    fun setApiKey(apiKey: String) {
        preferencesManager.saveApiKey(apiKey)
        _uiState.update { it.copy(apiKey = apiKey, showApiKeyDialog = false) }
    }
    
    fun startNewConversation() {
        currentConversationId = UUID.randomUUID().toString()
        _uiState.update { 
            it.copy(
                messages = emptyList(),
                conversationTitle = "New Conversation",
                isLoading = false
            )
        }
    }
    
    fun loadConversation(conversationId: String) {
        currentConversationId = conversationId
        viewModelScope.launch {
            chatRepository.getMessagesForConversation(conversationId).collect { messages ->
                _uiState.update { it.copy(messages = messages) }
            }
            
            val conversation = chatRepository.getConversationById(conversationId)
            conversation?.let { conv ->
                _uiState.update { it.copy(conversationTitle = conv.title) }
            }
        }
    }
    
    fun sendMessage(content: String) {
        val currentState = _uiState.value
        
        if (currentState.apiKey.isBlank()) {
            _uiState.update { it.copy(showApiKeyDialog = true) }
            return
        }
        
        val userMessage = Message(
            id = UUID.randomUUID().toString(),
            conversationId = currentConversationId ?: return,
            role = MessageRole.USER,
            content = content,
            timestamp = Date()
        )
        
        viewModelScope.launch {
            // Add user message
            chatRepository.insertMessage(userMessage)
            
            // Update conversation title if it's the first message
            if (currentState.messages.isEmpty()) {
                val title = if (content.length > 50) content.take(50) + "..." else content
                _uiState.update { it.copy(conversationTitle = title) }
                
                val conversation = Conversation(
                    id = currentConversationId!!,
                    title = title,
                    createdAt = Date(),
                    updatedAt = Date(),
                    messageCount = 1
                )
                chatRepository.insertConversation(conversation)
            }
            
            // Set loading state
            _uiState.update { it.copy(isLoading = true) }
            
            // Prepare messages for API
            val allMessages = currentState.messages + userMessage
            val systemMessage = Message(
                id = "system",
                conversationId = currentConversationId!!,
                role = MessageRole.SYSTEM,
                content = currentState.settings.systemPrompt,
                timestamp = Date()
            )
            val messagesForApi = listOf(systemMessage) + allMessages
            
            // Send to OpenRouter
            val result = chatRepository.sendMessageToOpenRouter(
                apiKey = currentState.apiKey,
                messages = messagesForApi,
                settings = currentState.settings
            )
            
            result.fold(
                onSuccess = { responseContent ->
                    val assistantMessage = Message(
                        id = UUID.randomUUID().toString(),
                        conversationId = currentConversationId!!,
                        role = MessageRole.ASSISTANT,
                        content = responseContent,
                        timestamp = Date()
                    )
                    chatRepository.insertMessage(assistantMessage)
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            errorMessage = error.message ?: "Unknown error occurred"
                        )
                    }
                }
            )
            
            _uiState.update { it.copy(isLoading = false) }
        }
    }
    
    fun toggleMessageFavorite(messageId: String) {
        viewModelScope.launch {
            val message = _uiState.value.messages.find { it.id == messageId }
            message?.let {
                chatRepository.toggleMessageFavorite(messageId, !it.isFavorite)
            }
        }
    }
    
    fun editMessage(messageId: String, newContent: String) {
        viewModelScope.launch {
            chatRepository.editMessage(messageId, newContent)
        }
    }
    
    fun regenerateResponse(messageId: String) {
        viewModelScope.launch {
            val messages = _uiState.value.messages
            val messageIndex = messages.indexOfFirst { it.id == messageId }
            if (messageIndex > 0) {
                val previousMessages = messages.take(messageIndex)
                val lastUserMessage = previousMessages.lastOrNull { it.role == MessageRole.USER }
                lastUserMessage?.let {
                    sendMessage(it.content)
                }
            }
        }
    }
    
    fun addReaction(messageId: String, reaction: String) {
        viewModelScope.launch {
            val message = _uiState.value.messages.find { it.id == messageId }
            message?.let {
                val newReactions = if (it.reactions.contains(reaction)) {
                    it.reactions - reaction
                } else {
                    it.reactions + reaction
                }
                chatRepository.updateMessageReactions(messageId, newReactions)
            }
        }
    }
    
    fun clearCurrentChat() {
        startNewConversation()
    }
    
    fun updateSettings(settings: ChatSettings) {
        preferencesManager.saveChatSettings(settings)
        _uiState.update { it.copy(settings = settings) }
    }
    
    fun dismissError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
    
    fun showApiKeyDialog() {
        _uiState.update { it.copy(showApiKeyDialog = true) }
    }
    
    fun hideApiKeyDialog() {
        _uiState.update { it.copy(showApiKeyDialog = false) }
    }
}

data class ChatUiState(
    val messages: List<Message> = emptyList(),
    val isLoading: Boolean = false,
    val apiKey: String = "",
    val conversationTitle: String = "New Conversation",
    val settings: ChatSettings = ChatSettings(),
    val errorMessage: String? = null,
    val showApiKeyDialog: Boolean = false
)

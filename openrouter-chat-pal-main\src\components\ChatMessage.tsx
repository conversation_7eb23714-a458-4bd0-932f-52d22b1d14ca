
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Edit2, <PERSON>ota<PERSON><PERSON>c<PERSON>, MessageSquare } from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isFavorite?: boolean;
  reactions?: string[];
  isEdited?: boolean;
}

interface ChatMessageProps {
  message: Message;
  onCopy: (content: string) => void;
  onToggleFavorite: (messageId: string) => void;
  onEditMessage: (messageId: string, newContent: string) => void;
  onRegenerateResponse: (messageId: string) => void;
  onReact: (messageId: string, reaction: string) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  onCopy,
  onToggleFavorite,
  onEditMessage,
  onRegenerateResponse,
  onReact
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [showActions, setShowActions] = useState(false);
  const isUser = message.role === 'user';

  const handleSaveEdit = () => {
    if (editContent.trim() !== message.content) {
      onEditMessage(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditContent(message.content);
    setIsEditing(false);
  };

  const reactions = ['👍', '👎', '❤️', '😊', '🤔', '💡'];

  return (
    <div 
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} group relative`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`flex gap-2 sm:gap-3 max-w-[85%] sm:max-w-3xl ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
          isUser 
            ? 'gradient-primary' 
            : 'bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600'
        }`}>
          {isUser ? (
            <User className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
          ) : (
            <Bot className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600 dark:text-gray-300" />
          )}
        </div>

        {/* Message Content */}
        <div className={`relative ${isUser ? 'items-end' : 'items-start'} flex flex-col min-w-0 flex-1`}>
          <div className={`px-3 sm:px-4 py-2 sm:py-3 rounded-xl sm:rounded-2xl shadow-soft border transition-all duration-200 hover:shadow-glow ${
            isUser
              ? 'gradient-primary text-white rounded-br-md'
              : 'glass border-white/20 text-gray-900 dark:text-gray-100 rounded-bl-md'
          } ${message.isFavorite ? 'ring-2 ring-yellow-300' : ''}`}>
            {isEditing ? (
              <div className="space-y-2">
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="w-full p-2 border border-gray-200 dark:border-gray-600 rounded-md resize-none text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 text-sm"
                  rows={3}
                  autoFocus
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleSaveEdit}
                    className="px-3 py-1 bg-green-500 text-white rounded-md text-sm hover:bg-green-600 transition-colors"
                  >
                    Save
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="px-3 py-1 bg-gray-500 text-white rounded-md text-sm hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <p className="text-sm sm:text-base whitespace-pre-wrap leading-relaxed break-words">{message.content}</p>
                {message.isEdited && (
                  <span className={`text-xs mt-1 block ${isUser ? 'text-blue-100' : 'text-gray-400'}`}>
                    (edited)
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex gap-1 mt-1 flex-wrap">
              {message.reactions.map((reaction, index) => (
                <span key={index} className="px-2 py-1 glass rounded-full text-xs border border-white/20">
                  {reaction}
                </span>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          {(showActions || isEditing) && !isEditing && (
            <div className={`absolute -top-2 flex items-center gap-1 transition-all duration-200 ${
              isUser ? '-right-2' : '-left-2'
            }`}>
              <div className="flex items-center gap-1 glass-strong rounded-lg p-1 border border-white/20 shadow-soft">
                <button
                  onClick={() => onCopy(message.content)}
                  className="p-1 sm:p-1.5 rounded-md hover:bg-white/20 transition-colors"
                  title="Copy message"
                >
                  <Copy className="w-3 h-3 sm:w-4 sm:h-4" />
                </button>
                
                <button
                  onClick={() => onToggleFavorite(message.id)}
                  className={`p-1 sm:p-1.5 rounded-md hover:bg-white/20 transition-colors ${
                    message.isFavorite ? 'text-yellow-500' : ''
                  }`}
                  title="Toggle favorite"
                >
                  <Star className={`w-3 h-3 sm:w-4 sm:h-4 ${message.isFavorite ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  onClick={() => setIsEditing(true)}
                  className="p-1 sm:p-1.5 rounded-md hover:bg-white/20 transition-colors"
                  title="Edit message"
                >
                  <Edit2 className="w-3 h-3 sm:w-4 sm:h-4" />
                </button>
                
                {!isUser && (
                  <button
                    onClick={() => onRegenerateResponse(message.id)}
                    className="p-1 sm:p-1.5 rounded-md hover:bg-white/20 transition-colors"
                    title="Regenerate response"
                  >
                    <RotateCcw className="w-3 h-3 sm:w-4 sm:h-4" />
                  </button>
                )}
                
                {/* Reaction Menu */}
                <div className="relative group/reactions">
                  <button 
                    className="p-1 sm:p-1.5 rounded-md hover:bg-white/20 transition-colors"
                    title="Add reaction"
                  >
                    <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4" />
                  </button>
                  <div className="absolute top-full mt-1 left-0 glass-strong border border-white/20 rounded-lg shadow-glow p-2 flex gap-1 opacity-0 group-hover/reactions:opacity-100 transition-opacity z-10">
                    {reactions.map((reaction) => (
                      <button
                        key={reaction}
                        onClick={() => onReact(message.id, reaction)}
                        className="p-1 hover:bg-white/20 rounded transition-colors text-sm"
                      >
                        {reaction}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Timestamp */}
          <span className={`text-xs text-gray-500 dark:text-gray-400 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;


import React, { useState, useEffect } from 'react';
import { Search, Plus, Trash2, Download, Star, MessageSquare, X, Edit2, Calendar, Filter } from 'lucide-react';
import { ConversationManager, Conversation } from '@/utils/conversationManager';
import { useToast } from '@/hooks/use-toast';

interface ConversationSidebarProps {
  currentConversationId: string | null;
  onSelectConversation: (conversation: Conversation) => void;
  onNewConversation: () => void;
  onClose: () => void;
  isOpen: boolean;
}

const ConversationSidebar: React.FC<ConversationSidebarProps> = ({
  currentConversationId,
  onSelectConversation,
  onNewConversation,
  onClose,
  isOpen
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterFavorites, setFilterFavorites] = useState(false);
  const [sortBy, setSortBy] = useState<'date' | 'title'>('date');
  const { toast } = useToast();

  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = () => {
    const allConversations = ConversationManager.getAllConversations();
    setConversations(allConversations);
  };

  const handleDeleteConversation = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    ConversationManager.deleteConversation(id);
    loadConversations();
    toast({
      title: "Conversation Deleted",
      description: "The conversation has been permanently removed.",
    });
  };

  const handleExportConversation = (conversation: Conversation, format: 'json' | 'txt' | 'md', e: React.MouseEvent) => {
    e.stopPropagation();
    const content = ConversationManager.exportConversation(conversation, format);
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${conversation.title}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Conversation Exported",
      description: `Conversation exported as ${format.toUpperCase()} file.`,
    });
  };

  const filteredConversations = conversations
    .filter(conv => {
      const matchesSearch = searchQuery === '' || 
        conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        conv.messages.some(msg => msg.content.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesFavorites = !filterFavorites || 
        conv.messages.some(msg => msg.isFavorite);
      
      return matchesSearch && matchesFavorites;
    })
    .sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      }
      return a.title.localeCompare(b.title);
    });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:relative lg:inset-auto">
      <div className="absolute inset-0 bg-black/50 lg:hidden" onClick={onClose} />
      <div className="absolute left-0 top-0 h-full w-80 bg-white border-r border-gray-200 shadow-xl lg:shadow-none lg:relative">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 lg:hidden"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="space-y-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            {/* Filters */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setFilterFavorites(!filterFavorites)}
                className={`px-3 py-1 rounded-md text-sm transition-colors ${
                  filterFavorites 
                    ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Star className="w-3 h-3 inline mr-1" />
                Favorites
              </button>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'date' | 'title')}
                className="px-2 py-1 text-sm border border-gray-200 rounded-md"
              >
                <option value="date">By Date</option>
                <option value="title">By Title</option>
              </select>
            </div>
            
            {/* New Conversation */}
            <button
              onClick={onNewConversation}
              className="w-full flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all"
            >
              <Plus className="w-4 h-4" />
              New Conversation
            </button>
          </div>
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-y-auto p-2">
          {filteredConversations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? 'No conversations found' : 'No conversations yet'}
            </div>
          ) : (
            <div className="space-y-1">
              {filteredConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => onSelectConversation(conversation)}
                  className={`group relative p-3 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                    currentConversationId === conversation.id ? 'bg-blue-50 border border-blue-200' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 truncate">
                        {conversation.title}
                      </h3>
                      <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                        <Calendar className="w-3 h-3" />
                        {conversation.updatedAt.toLocaleDateString()}
                        <MessageSquare className="w-3 h-3 ml-1" />
                        {conversation.messages.length}
                      </div>
                      {conversation.messages.some(msg => msg.isFavorite) && (
                        <Star className="w-3 h-3 text-yellow-500 mt-1" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={(e) => handleExportConversation(conversation, 'json', e)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Export as JSON"
                      >
                        <Download className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => handleDeleteConversation(conversation.id, e)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Delete conversation"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConversationSidebar;


import { sendMessage } from '@/utils/openrouter';
import { ConversationManager, Conversation } from '@/utils/conversationManager';
import { Message, ChatSettings } from '@/hooks/useChat';

interface UseChatHandlersProps {
  apiKey: string;
  setApiKey: (key: string) => void;
  messages: Message[];
  setMessages: (messages: Message[] | ((prev: Message[]) => Message[])) => void;
  setIsLoading: (loading: boolean) => void;
  setShowApiKeyInput: (show: boolean) => void;
  conversationTitle: string;
  setConversationTitle: (title: string) => void;
  currentConversationId: string | null;
  setCurrentConversationId: (id: string | null) => void;
  settings: ChatSettings;
  saveCurrentConversation: () => void;
  toast: any;
}

export const useChatHandlers = ({
  apiKey,
  setApiKey,
  messages,
  setMessages,
  setIsLoading,
  setShowApiKeyInput,
  conversationTitle,
  setConversationTitle,
  currentConversationId,
  setCurrentConversationId,
  settings,
  saveCurrentConversation,
  toast,
}: UseChatHandlersProps) => {

  const handleSendMessage = async (content: string) => {
    if (!apiKey) {
      setShowApiKeyInput(true);
      toast({
        title: "API Key Required",
        description: "Please enter your OpenRouter API key to start chatting.",
        variant: "destructive",
      });
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: new Date(),
    };

    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setIsLoading(true);

    // Auto-generate title from first message
    if (messages.length === 0) {
      const title = content.length > 50 ? content.substring(0, 50) + '...' : content;
      setConversationTitle(title);
    }

    try {
      const messagesToSend = [
        { role: 'system' as const, content: settings.systemPrompt },
        ...newMessages.map(msg => ({ role: msg.role, content: msg.content }))
      ];

      const response = await sendMessage(apiKey, messagesToSend, {
        model: settings.model,
        temperature: settings.temperature,
        max_tokens: settings.maxTokens
      });
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response,
        timestamp: new Date(),
      };

      const finalMessages = [...newMessages, assistantMessage];
      setMessages(finalMessages);
      
      // Auto-save conversation
      setTimeout(() => {
        const conversation: Conversation = {
          id: currentConversationId || Date.now().toString(),
          title: conversationTitle,
          messages: finalMessages,
          createdAt: new Date(),
          updatedAt: new Date(),
          model: settings.model,
          temperature: settings.temperature
        };
        ConversationManager.saveConversation(conversation);
        if (!currentConversationId) {
          setCurrentConversationId(conversation.id);
        }
      }, 100);

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message. Please check your API key and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApiKeySubmit = (key: string) => {
    setApiKey(key);
    localStorage.setItem('openrouter_api_key', key);
    setShowApiKeyInput(false);
    toast({
      title: "API Key Saved",
      description: "Your OpenRouter API key has been saved securely.",
    });
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "Copied!",
      description: "Message copied to clipboard.",
    });
  };

  const handleToggleFavorite = (messageId: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, isFavorite: !msg.isFavorite }
        : msg
    ));
    saveCurrentConversation();
  };

  const handleEditMessage = (messageId: string, newContent: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, content: newContent, isEdited: true }
        : msg
    ));
    saveCurrentConversation();
  };

  const handleRegenerateResponse = async (messageId: string) => {
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    const messagesUpToRegenerate = messages.slice(0, messageIndex);
    setMessages(messagesUpToRegenerate);
    setIsLoading(true);

    try {
      const messagesToSend = [
        { role: 'system' as const, content: settings.systemPrompt },
        ...messagesUpToRegenerate.map(msg => ({ role: msg.role, content: msg.content }))
      ];

      const response = await sendMessage(apiKey, messagesToSend, {
        model: settings.model,
        temperature: settings.temperature,
        max_tokens: settings.maxTokens
      });
      
      const newAssistantMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: response,
        timestamp: new Date(),
      };

      setMessages([...messagesUpToRegenerate, newAssistantMessage]);
    } catch (error) {
      console.error('Error regenerating response:', error);
      toast({
        title: "Error",
        description: "Failed to regenerate response.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReact = (messageId: string, reaction: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { 
            ...msg, 
            reactions: msg.reactions ? [...msg.reactions, reaction] : [reaction]
          }
        : msg
    ));
    saveCurrentConversation();
  };

  return {
    handleSendMessage,
    handleApiKeySubmit,
    copyMessage,
    handleToggleFavorite,
    handleEditMessage,
    handleRegenerateResponse,
    handleReact,
  };
};


import React, { useState } from 'react';
import { X, Setting<PERSON>, Thermometer, Cpu, MessageSquare, Download, Upload } from 'lucide-react';

interface AdvancedSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  settings: {
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
  };
  onSettingsChange: (settings: any) => void;
}

const AVAILABLE_MODELS = [
  { id: 'openai/gpt-4.1', name: 'GPT-4.1', description: 'Most capable model' },
  { id: 'openai/gpt-4o', name: 'GPT-4o', description: 'Optimized for speed' },
  { id: 'openai/gpt-4o-mini', name: 'GPT-4o Mini', description: 'Cost-effective' },
  { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet', description: 'Great for analysis' },
  { id: 'google/gemini-pro', name: '<PERSON> Pro', description: 'Google\'s flagship model' },
];

const PROMPT_TEMPLATES = [
  { name: 'Default', prompt: 'You are a helpful assistant.' },
  { name: 'Creative Writer', prompt: 'You are a creative writing assistant. Help users craft engaging stories, poems, and creative content.' },
  { name: 'Code Expert', prompt: 'You are an expert programmer. Provide clean, efficient code solutions and explain complex programming concepts.' },
  { name: 'Academic Tutor', prompt: 'You are an academic tutor. Explain concepts clearly and help students understand complex topics.' },
  { name: 'Business Advisor', prompt: 'You are a business consultant. Provide strategic advice and help solve business challenges.' },
];

const AdvancedSettings: React.FC<AdvancedSettingsProps> = ({
  isOpen,
  onClose,
  settings,
  onSettingsChange
}) => {
  const [tempSettings, setTempSettings] = useState(settings);

  const handleSave = () => {
    onSettingsChange(tempSettings);
    onClose();
  };

  const exportSettings = () => {
    const blob = new Blob([JSON.stringify(tempSettings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'chat-settings.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          setTempSettings(importedSettings);
        } catch (error) {
          console.error('Error importing settings:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
              <Settings className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Advanced Settings</h2>
              <p className="text-sm text-gray-500">Customize your AI experience</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(80vh-140px)]">
          <div className="space-y-6">
            {/* Model Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                <Cpu className="w-4 h-4" />
                AI Model
              </label>
              <div className="grid gap-2">
                {AVAILABLE_MODELS.map((model) => (
                  <label key={model.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      name="model"
                      value={model.id}
                      checked={tempSettings.model === model.id}
                      onChange={(e) => setTempSettings({...tempSettings, model: e.target.value})}
                      className="mr-3"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{model.name}</div>
                      <div className="text-sm text-gray-500">{model.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Temperature */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                <Thermometer className="w-4 h-4" />
                Creativity Level (Temperature: {tempSettings.temperature})
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={tempSettings.temperature}
                onChange={(e) => setTempSettings({...tempSettings, temperature: parseFloat(e.target.value)})}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Focused</span>
                <span>Balanced</span>
                <span>Creative</span>
              </div>
            </div>

            {/* Max Tokens */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                Max Response Length
              </label>
              <input
                type="number"
                min="100"
                max="4000"
                value={tempSettings.maxTokens}
                onChange={(e) => setTempSettings({...tempSettings, maxTokens: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">Higher values allow longer responses but cost more</p>
            </div>

            {/* System Prompt Templates */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Assistant Personality
              </label>
              <div className="grid gap-2 mb-3">
                {PROMPT_TEMPLATES.map((template) => (
                  <button
                    key={template.name}
                    onClick={() => setTempSettings({...tempSettings, systemPrompt: template.prompt})}
                    className={`text-left p-3 border rounded-lg transition-colors ${
                      tempSettings.systemPrompt === template.prompt
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{template.name}</div>
                    <div className="text-sm text-gray-500 truncate">{template.prompt}</div>
                  </button>
                ))}
              </div>
              
              <textarea
                value={tempSettings.systemPrompt}
                onChange={(e) => setTempSettings({...tempSettings, systemPrompt: e.target.value})}
                placeholder="Custom system prompt..."
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                rows={3}
              />
            </div>

            {/* Import/Export */}
            <div className="flex gap-3">
              <button
                onClick={exportSettings}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Download className="w-4 h-4" />
                Export Settings
              </button>
              <label className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <Upload className="w-4 h-4" />
                Import Settings
                <input
                  type="file"
                  accept=".json"
                  onChange={importSettings}
                  className="hidden"
                />
              </label>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-100">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all shadow-md hover:shadow-lg"
          >
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSettings;

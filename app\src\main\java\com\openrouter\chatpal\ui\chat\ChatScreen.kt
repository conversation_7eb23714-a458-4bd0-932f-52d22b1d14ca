package com.openrouter.chatpal.ui.chat

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.openrouter.chatpal.ui.chat.components.*
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatScreen(
    uiState: ChatUiState,
    onSendMessage: (String) -> Unit,
    onToggleMessageFavorite: (String) -> Unit,
    onEditMessage: (String, String) -> Unit,
    onRegenerateResponse: (String) -> Unit,
    onAddReaction: (String, String) -> Unit,
    onCopyMessage: (String) -> Unit,
    onNavigateToConversations: () -> Unit,
    onNavigateToSettings: () -> Unit,
    onNewConversation: () -> Unit,
    onClearChat: () -> Unit,
    onSetApiKey: (String) -> Unit
) {
    val listState = rememberLazyListState()
    val scope = rememberCoroutineScope()
    
    // Auto-scroll to bottom when new messages arrive
    LaunchedEffect(uiState.messages.size) {
        if (uiState.messages.isNotEmpty()) {
            scope.launch {
                listState.animateScrollToItem(uiState.messages.size - 1)
            }
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text(uiState.conversationTitle) },
            navigationIcon = {
                IconButton(onClick = onNavigateToConversations) {
                    Icon(Icons.Default.Menu, contentDescription = "Conversations")
                }
            },
            actions = {
                if (uiState.messages.isNotEmpty()) {
                    IconButton(onClick = onClearChat) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear Chat")
                    }
                }
                IconButton(onClick = onNewConversation) {
                    Icon(Icons.Default.Add, contentDescription = "New Chat")
                }
                IconButton(onClick = onNavigateToSettings) {
                    Icon(Icons.Default.Settings, contentDescription = "Settings")
                }
            }
        )
        
        // Chat Content
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            if (uiState.messages.isEmpty()) {
                // Welcome Screen
                WelcomeScreen(
                    hasApiKey = uiState.apiKey.isNotBlank(),
                    onShowApiKeyDialog = { onSetApiKey("") }
                )
            } else {
                // Messages List
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(uiState.messages) { message ->
                        MessageItem(
                            message = message,
                            onToggleFavorite = { onToggleMessageFavorite(message.id) },
                            onEdit = { newContent -> onEditMessage(message.id, newContent) },
                            onRegenerate = { onRegenerateResponse(message.id) },
                            onAddReaction = { reaction -> onAddReaction(message.id, reaction) },
                            onCopy = { onCopyMessage(message.content) }
                        )
                    }
                    
                    // Loading indicator
                    if (uiState.isLoading) {
                        item {
                            Box(
                                modifier = Modifier.fillMaxWidth(),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }
                    }
                }
            }
        }
        
        // Message Input
        MessageInputField(
            onSendMessage = onSendMessage,
            enabled = uiState.apiKey.isNotBlank() && !uiState.isLoading
        )
    }
    
    // API Key Dialog
    if (uiState.showApiKeyDialog) {
        ApiKeyDialog(
            currentApiKey = uiState.apiKey,
            onSaveApiKey = onSetApiKey,
            onDismiss = { /* Handle dismiss */ }
        )
    }
    
    // Error Snackbar
    uiState.errorMessage?.let { error ->
        LaunchedEffect(error) {
            // Show snackbar for error
        }
    }
}


import React, { useState, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({ onSendMessage, disabled = false }) => {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 glass-strong border-t border-white/20 z-20">
      <div className="max-w-6xl mx-auto px-3 sm:px-6 py-3 sm:py-4">
        <form onSubmit={handleSubmit} className="relative">
          <div className="flex items-end gap-2 sm:gap-3 glass rounded-xl sm:rounded-2xl border border-white/20 p-3 sm:p-4 shadow-soft">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={disabled ? "Enter your API key to start chatting..." : "Type your message... (Shift + Enter for new line)"}
              disabled={disabled}
              className="flex-1 resize-none border-none outline-none text-sm sm:text-base leading-relaxed max-h-32 bg-transparent placeholder-gray-500 dark:placeholder-gray-400 disabled:opacity-50"
              rows={1}
            />
            <button
              type="submit"
              disabled={!message.trim() || disabled}
              className="p-2 sm:p-2.5 gradient-primary text-white rounded-lg sm:rounded-xl hover:shadow-glow disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-soft btn-modern flex-shrink-0"
            >
              <Send className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MessageInput;

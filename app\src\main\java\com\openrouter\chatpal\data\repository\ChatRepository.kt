package com.openrouter.chatpal.data.repository

import com.openrouter.chatpal.data.database.ConversationDao
import com.openrouter.chatpal.data.database.MessageDao
import com.openrouter.chatpal.data.model.*
import com.openrouter.chatpal.data.network.OpenRouterApi
import com.openrouter.chatpal.data.network.OpenRouterApiConstants
import kotlinx.coroutines.flow.Flow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ChatRepository @Inject constructor(
    private val messageDao: MessageDao,
    private val conversationDao: ConversationDao,
    private val openRouterApi: OpenRouterApi
) {
    
    // Message operations
    fun getMessagesForConversation(conversationId: String): Flow<List<Message>> {
        return messageDao.getMessagesForConversation(conversationId)
    }
    
    suspend fun insertMessage(message: Message) {
        messageDao.insertMessage(message)
    }
    
    suspend fun updateMessage(message: Message) {
        messageDao.updateMessage(message)
    }
    
    suspend fun deleteMessage(message: Message) {
        messageDao.deleteMessage(message)
    }
    
    suspend fun toggleMessageFavorite(messageId: String, isFavorite: Boolean) {
        messageDao.updateMessageFavorite(messageId, isFavorite)
    }
    
    suspend fun updateMessageReactions(messageId: String, reactions: List<String>) {
        messageDao.updateMessageReactions(messageId, reactions)
    }
    
    suspend fun editMessage(messageId: String, content: String) {
        messageDao.updateMessageContent(messageId, content)
    }
    
    suspend fun searchMessages(query: String): List<Message> {
        return messageDao.searchMessages(query)
    }
    
    // Conversation operations
    fun getAllConversations(): Flow<List<Conversation>> {
        return conversationDao.getAllConversations()
    }
    
    fun getFavoriteConversations(): Flow<List<Conversation>> {
        return conversationDao.getFavoriteConversations()
    }
    
    suspend fun insertConversation(conversation: Conversation) {
        conversationDao.insertConversation(conversation)
    }
    
    suspend fun updateConversation(conversation: Conversation) {
        conversationDao.updateConversation(conversation)
    }
    
    suspend fun deleteConversation(conversationId: String) {
        conversationDao.deleteConversationById(conversationId)
        messageDao.deleteMessagesForConversation(conversationId)
    }
    
    suspend fun getConversationById(id: String): Conversation? {
        return conversationDao.getConversationById(id)
    }
    
    suspend fun searchConversations(query: String): List<Conversation> {
        return conversationDao.searchConversations(query)
    }
    
    // OpenRouter API operations
    suspend fun sendMessageToOpenRouter(
        apiKey: String,
        messages: List<Message>,
        settings: ChatSettings
    ): Result<String> {
        return try {
            val openRouterMessages = messages.map { message ->
                OpenRouterMessage(
                    role = when (message.role) {
                        MessageRole.USER -> "user"
                        MessageRole.ASSISTANT -> "assistant"
                        MessageRole.SYSTEM -> "system"
                    },
                    content = message.content
                )
            }
            
            val request = OpenRouterRequest(
                model = settings.model,
                messages = openRouterMessages,
                temperature = settings.temperature,
                max_tokens = settings.maxTokens
            )
            
            val response = openRouterApi.sendMessage(
                authorization = "Bearer $apiKey",
                referer = OpenRouterApiConstants.REFERER,
                title = OpenRouterApiConstants.APP_TITLE,
                request = request
            )
            
            if (response.isSuccessful) {
                val responseBody = response.body()
                val content = responseBody?.choices?.firstOrNull()?.message?.content
                if (content != null) {
                    Result.success(content)
                } else {
                    Result.failure(Exception("Invalid response format"))
                }
            } else {
                Result.failure(Exception("API Error: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Export conversation
    suspend fun exportConversation(conversationId: String, format: String): String {
        val conversation = getConversationById(conversationId)
        val messages = messageDao.getMessagesForConversationSync(conversationId)
        
        return when (format.lowercase()) {
            "json" -> exportAsJson(conversation, messages)
            "txt" -> exportAsText(conversation, messages)
            "md" -> exportAsMarkdown(conversation, messages)
            else -> exportAsText(conversation, messages)
        }
    }
    
    private fun exportAsJson(conversation: Conversation?, messages: List<Message>): String {
        val data = mapOf(
            "conversation" to conversation,
            "messages" to messages
        )
        return com.google.gson.Gson().toJson(data)
    }
    
    private fun exportAsText(conversation: Conversation?, messages: List<Message>): String {
        val sb = StringBuilder()
        sb.appendLine("Conversation: ${conversation?.title ?: "Unknown"}")
        sb.appendLine("Created: ${conversation?.createdAt ?: Date()}")
        sb.appendLine("=" * 50)
        sb.appendLine()
        
        messages.forEach { message ->
            val role = when (message.role) {
                MessageRole.USER -> "You"
                MessageRole.ASSISTANT -> "Assistant"
                MessageRole.SYSTEM -> "System"
            }
            sb.appendLine("[$role] ${message.timestamp}")
            sb.appendLine(message.content)
            sb.appendLine()
        }
        
        return sb.toString()
    }
    
    private fun exportAsMarkdown(conversation: Conversation?, messages: List<Message>): String {
        val sb = StringBuilder()
        sb.appendLine("# ${conversation?.title ?: "Conversation"}")
        sb.appendLine()
        sb.appendLine("**Created:** ${conversation?.createdAt ?: Date()}")
        sb.appendLine()
        
        messages.forEach { message ->
            val role = when (message.role) {
                MessageRole.USER -> "**You**"
                MessageRole.ASSISTANT -> "**Assistant**"
                MessageRole.SYSTEM -> "**System**"
            }
            sb.appendLine("## $role")
            sb.appendLine()
            sb.appendLine(message.content)
            sb.appendLine()
        }
        
        return sb.toString()
    }
}

package com.openrouter.chatpal.ui.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.openrouter.chatpal.data.model.ChatSettings
import com.openrouter.chatpal.data.repository.ChatRepository
import com.openrouter.chatpal.utils.PreferencesManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val preferencesManager: PreferencesManager,
    private val chatRepository: ChatRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        loadSettings()
    }
    
    private fun loadSettings() {
        val settings = preferencesManager.getChatSettings()
        val apiKey = preferencesManager.getApiKey()
        val themeMode = preferencesManager.getThemeMode()
        
        _uiState.update { 
            it.copy(
                settings = settings,
                apiKey = apiKey,
                hasApiKey = apiKey.isNotBlank(),
                themeMode = themeMode,
                availableModels = getAvailableModels()
            )
        }
    }
    
    private fun getAvailableModels(): List<String> {
        return listOf(
            "openai/gpt-4-turbo",
            "openai/gpt-4",
            "openai/gpt-3.5-turbo",
            "anthropic/claude-3-opus",
            "anthropic/claude-3-sonnet",
            "anthropic/claude-3-haiku",
            "google/gemini-pro",
            "meta-llama/llama-2-70b-chat",
            "mistralai/mixtral-8x7b-instruct"
        )
    }
    
    // API Key Management
    fun showApiKeyDialog() {
        _uiState.update { it.copy(showApiKeyDialog = true) }
    }
    
    fun hideApiKeyDialog() {
        _uiState.update { it.copy(showApiKeyDialog = false) }
    }
    
    fun saveApiKey(apiKey: String) {
        preferencesManager.saveApiKey(apiKey)
        _uiState.update { 
            it.copy(
                apiKey = apiKey,
                hasApiKey = apiKey.isNotBlank(),
                showApiKeyDialog = false
            )
        }
    }
    
    // Chat Settings
    fun updateModel(model: String) {
        val newSettings = _uiState.value.settings.copy(model = model)
        saveSettings(newSettings)
        _uiState.update { it.copy(showModelSelector = false) }
    }
    
    fun updateTemperature(temperature: Float) {
        val newSettings = _uiState.value.settings.copy(temperature = temperature)
        saveSettings(newSettings)
    }
    
    fun updateMaxTokens(maxTokens: Int) {
        val newSettings = _uiState.value.settings.copy(maxTokens = maxTokens)
        saveSettings(newSettings)
    }
    
    fun updateSystemPrompt(prompt: String) {
        val newSettings = _uiState.value.settings.copy(systemPrompt = prompt)
        saveSettings(newSettings)
        _uiState.update { it.copy(showSystemPromptDialog = false) }
    }
    
    private fun saveSettings(settings: ChatSettings) {
        preferencesManager.saveChatSettings(settings)
        _uiState.update { it.copy(settings = settings) }
    }
    
    // Model Selector
    fun showModelSelector() {
        _uiState.update { it.copy(showModelSelector = true) }
    }
    
    fun hideModelSelector() {
        _uiState.update { it.copy(showModelSelector = false) }
    }
    
    // System Prompt
    fun showSystemPromptDialog() {
        _uiState.update { it.copy(showSystemPromptDialog = true) }
    }
    
    fun hideSystemPromptDialog() {
        _uiState.update { it.copy(showSystemPromptDialog = false) }
    }
    
    // Theme
    fun showThemeSelector() {
        _uiState.update { it.copy(showThemeSelector = true) }
    }
    
    fun hideThemeSelector() {
        _uiState.update { it.copy(showThemeSelector = false) }
    }
    
    fun updateTheme(theme: String) {
        preferencesManager.saveThemeMode(theme)
        _uiState.update { 
            it.copy(
                themeMode = theme,
                showThemeSelector = false
            )
        }
    }
    
    // App Settings
    fun updateAutoSave(autoSave: Boolean) {
        _uiState.update { it.copy(autoSave = autoSave) }
    }
    
    fun updateShowTimestamps(showTimestamps: Boolean) {
        _uiState.update { it.copy(showTimestamps = showTimestamps) }
    }
    
    // Data Management
    fun exportAllData() {
        viewModelScope.launch {
            try {
                val exportData = chatRepository.exportAllData()
                _uiState.update { 
                    it.copy(
                        exportData = exportData,
                        successMessage = "Data exported successfully"
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = "Failed to export data: ${e.message}")
                }
            }
        }
    }
    
    fun importData() {
        // This would typically open a file picker
        _uiState.update { 
            it.copy(successMessage = "Import functionality would open file picker")
        }
    }
    
    fun showClearDataDialog() {
        _uiState.update { it.copy(showClearDataDialog = true) }
    }
    
    fun hideClearDataDialog() {
        _uiState.update { it.copy(showClearDataDialog = false) }
    }
    
    fun clearAllData() {
        viewModelScope.launch {
            try {
                chatRepository.clearAllData()
                preferencesManager.clearAllData()
                _uiState.update { 
                    it.copy(
                        showClearDataDialog = false,
                        successMessage = "All data cleared successfully"
                    )
                }
                loadSettings() // Reload default settings
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        showClearDataDialog = false,
                        errorMessage = "Failed to clear data: ${e.message}"
                    )
                }
            }
        }
    }
    
    // External Links
    fun openPrivacyPolicy() {
        // Would open browser with privacy policy URL
    }
    
    fun openLicenses() {
        // Would open licenses screen
    }
    
    // Error/Success handling
    fun clearMessages() {
        _uiState.update { 
            it.copy(
                errorMessage = null,
                successMessage = null,
                exportData = null
            )
        }
    }
}

data class SettingsUiState(
    val settings: ChatSettings = ChatSettings(),
    val apiKey: String = "",
    val hasApiKey: Boolean = false,
    val themeMode: String = "system",
    val autoSave: Boolean = true,
    val showTimestamps: Boolean = true,
    val availableModels: List<String> = emptyList(),
    val showApiKeyDialog: Boolean = false,
    val showModelSelector: Boolean = false,
    val showSystemPromptDialog: Boolean = false,
    val showThemeSelector: Boolean = false,
    val showClearDataDialog: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val exportData: String? = null
)

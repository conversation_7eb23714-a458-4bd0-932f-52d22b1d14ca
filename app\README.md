# OpenRouter Chat Pal - Android App

A modern Android application that brings the power of OpenRouter's AI models to your mobile device. This app is a complete conversion of the original React web application, featuring all the same functionality with a native Android experience.

## Features

### 🤖 AI Chat Interface
- **Multiple AI Models**: Support for GPT-4, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and more via OpenRouter
- **Real-time Conversations**: Instant responses with streaming support
- **Message Management**: Edit, regenerate, favorite, and react to messages
- **Smart Conversations**: Organized chat history with search and filtering

### 🎨 Modern UI/UX
- **Material Design 3**: Beautiful, modern interface following Google's design guidelines
- **Dark/Light Themes**: Automatic theme switching based on system preferences
- **Responsive Design**: Optimized for all screen sizes and orientations
- **Smooth Animations**: Fluid transitions and interactions

### ⚙️ Advanced Settings
- **Model Configuration**: Choose from various AI models with different capabilities
- **Temperature Control**: Fine-tune response creativity and randomness
- **Token Limits**: Control response length and cost
- **System Prompts**: Customize AI behavior with custom instructions

### 💾 Data Management
- **Local Storage**: All conversations stored securely on device
- **Export/Import**: Backup and restore your chat history
- **Search**: Find specific conversations and messages quickly
- **Favorites**: Mark important conversations for easy access

### 🔒 Security & Privacy
- **Encrypted Storage**: API keys stored using Android's encrypted preferences
- **Local Processing**: All data stays on your device
- **No Tracking**: Privacy-focused design with no analytics or tracking

## Technical Architecture

### Built With
- **Kotlin**: Modern, concise programming language for Android
- **Jetpack Compose**: Declarative UI toolkit for native Android
- **Room Database**: Local SQLite database with type-safe queries
- **Retrofit**: Type-safe HTTP client for API communication
- **Hilt**: Dependency injection for clean architecture
- **Material 3**: Latest Material Design components

### Architecture Pattern
- **MVVM**: Model-View-ViewModel architecture for separation of concerns
- **Repository Pattern**: Centralized data access layer
- **Clean Architecture**: Organized code structure for maintainability
- **Reactive Programming**: Kotlin Flows for reactive data streams

### Project Structure
```
app/
├── src/main/java/com/openrouter/chatpal/
│   ├── data/
│   │   ├── database/          # Room database entities and DAOs
│   │   ├── model/             # Data models and entities
│   │   ├── network/           # API interfaces and networking
│   │   └── repository/        # Data repository implementations
│   ├── di/                    # Dependency injection modules
│   ├── ui/
│   │   ├── chat/              # Chat screen and components
│   │   ├── conversations/     # Conversations list screen
│   │   ├── settings/          # Settings and configuration
│   │   └── theme/             # UI theming and styling
│   └── utils/                 # Utility classes and helpers
└── src/main/res/              # Android resources (layouts, strings, etc.)
```

## Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24+ (Android 7.0)
- OpenRouter API key from [openrouter.ai](https://openrouter.ai)

### Installation
1. **Import Project**: Open Android Studio and import this project
2. **Sync Dependencies**: Let Gradle sync all dependencies
3. **Build Project**: Build the project to ensure everything compiles
4. **Run App**: Deploy to device or emulator

### Configuration
1. **API Key**: Launch the app and enter your OpenRouter API key
2. **Model Selection**: Choose your preferred AI model in settings
3. **Customize**: Adjust temperature, tokens, and system prompts as needed

## Usage

### Starting a Conversation
1. Open the app and set your API key if not already configured
2. Type your message in the input field at the bottom
3. Tap the send button to start chatting with AI

### Managing Conversations
- **New Chat**: Tap the "+" button to start a new conversation
- **View History**: Tap the menu button to see all conversations
- **Search**: Use the search bar to find specific conversations
- **Export**: Long-press a conversation to export or delete

### Customizing Settings
- **Models**: Choose from GPT-4, Claude, Gemini, and more
- **Temperature**: Adjust creativity (0.0 = focused, 2.0 = creative)
- **Max Tokens**: Set response length limits
- **System Prompt**: Add custom instructions for the AI

## API Integration

The app integrates with OpenRouter's API to provide access to multiple AI models:

### Supported Models
- **OpenAI**: GPT-4 Turbo, GPT-4, GPT-3.5 Turbo
- **Anthropic**: Claude 3 Opus, Sonnet, Haiku
- **Google**: Gemini Pro
- **Meta**: Llama 2 70B Chat
- **Mistral**: Mixtral 8x7B Instruct

### API Features
- **Streaming**: Real-time response streaming for better UX
- **Error Handling**: Robust error handling and retry logic
- **Rate Limiting**: Automatic rate limit handling
- **Cost Tracking**: Monitor token usage and costs

## Development

### Building from Source
```bash
# Clone the repository
git clone <repository-url>

# Open in Android Studio
# File -> Open -> Select the project folder

# Build the project
./gradlew build

# Run tests
./gradlew test

# Install on device
./gradlew installDebug
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **OpenRouter**: For providing access to multiple AI models through a unified API
- **Material Design**: For the beautiful design system and components
- **Android Jetpack**: For the modern Android development tools and libraries
- **Open Source Community**: For the amazing libraries and tools that make this possible

## Support

For support, feature requests, or bug reports:
- Create an issue on GitHub
- Check the documentation
- Review existing issues for solutions

---

**Note**: This app requires an OpenRouter API key to function. You can get one for free at [openrouter.ai](https://openrouter.ai). The app stores your API key securely on your device and never shares it with third parties.

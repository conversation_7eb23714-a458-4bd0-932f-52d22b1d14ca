package com.openrouter.chatpal.ui.settings

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.openrouter.chatpal.data.model.ChatSettings
import com.openrouter.chatpal.ui.settings.components.*
import com.openrouter.chatpal.ui.theme.ChatPalTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SettingsActivity : ComponentActivity() {
    
    private val viewModel: SettingsViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            ChatPalTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SettingsScreen(
                        viewModel = viewModel,
                        onBackPressed = { finish() }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel,
    onBackPressed: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val scrollState = rememberScrollState()
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text("Settings") },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            }
        )
        
        // Settings Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // API Configuration
            SettingsSection(title = "API Configuration") {
                SettingsItem(
                    title = "OpenRouter API Key",
                    subtitle = if (uiState.hasApiKey) "Configured" else "Not set",
                    icon = Icons.Default.Key,
                    onClick = { viewModel.showApiKeyDialog() }
                )
            }
            
            // Chat Settings
            SettingsSection(title = "Chat Settings") {
                // Model Selection
                SettingsItem(
                    title = "AI Model",
                    subtitle = uiState.settings.model,
                    icon = Icons.Default.SmartToy,
                    onClick = { viewModel.showModelSelector() }
                )
                
                // Temperature
                SettingsSlider(
                    title = "Temperature",
                    subtitle = "Controls randomness (${uiState.settings.temperature})",
                    value = uiState.settings.temperature,
                    onValueChange = { viewModel.updateTemperature(it) },
                    valueRange = 0f..2f
                )
                
                // Max Tokens
                SettingsSlider(
                    title = "Max Tokens",
                    subtitle = "Maximum response length (${uiState.settings.maxTokens})",
                    value = uiState.settings.maxTokens.toFloat(),
                    onValueChange = { viewModel.updateMaxTokens(it.toInt()) },
                    valueRange = 100f..4000f
                )
                
                // System Prompt
                SettingsItem(
                    title = "System Prompt",
                    subtitle = if (uiState.settings.systemPrompt.isNotBlank()) "Custom" else "Default",
                    icon = Icons.Default.Psychology,
                    onClick = { viewModel.showSystemPromptDialog() }
                )
            }
            
            // App Settings
            SettingsSection(title = "App Settings") {
                SettingsItem(
                    title = "Theme",
                    subtitle = uiState.themeMode.replaceFirstChar { it.uppercase() },
                    icon = Icons.Default.Palette,
                    onClick = { viewModel.showThemeSelector() }
                )
                
                SettingsSwitch(
                    title = "Auto-save conversations",
                    subtitle = "Automatically save chat history",
                    checked = uiState.autoSave,
                    onCheckedChange = { viewModel.updateAutoSave(it) }
                )
                
                SettingsSwitch(
                    title = "Show timestamps",
                    subtitle = "Display message timestamps",
                    checked = uiState.showTimestamps,
                    onCheckedChange = { viewModel.updateShowTimestamps(it) }
                )
            }
            
            // Data Management
            SettingsSection(title = "Data Management") {
                SettingsItem(
                    title = "Export All Data",
                    subtitle = "Export conversations and settings",
                    icon = Icons.Default.Download,
                    onClick = { viewModel.exportAllData() }
                )
                
                SettingsItem(
                    title = "Import Data",
                    subtitle = "Import conversations from file",
                    icon = Icons.Default.Upload,
                    onClick = { viewModel.importData() }
                )
                
                SettingsItem(
                    title = "Clear All Data",
                    subtitle = "Delete all conversations and settings",
                    icon = Icons.Default.DeleteForever,
                    onClick = { viewModel.showClearDataDialog() },
                    isDestructive = true
                )
            }
            
            // About
            SettingsSection(title = "About") {
                SettingsItem(
                    title = "Version",
                    subtitle = "1.0.0",
                    icon = Icons.Default.Info
                )
                
                SettingsItem(
                    title = "Privacy Policy",
                    subtitle = "View our privacy policy",
                    icon = Icons.Default.PrivacyTip,
                    onClick = { viewModel.openPrivacyPolicy() }
                )
                
                SettingsItem(
                    title = "Open Source Licenses",
                    subtitle = "View third-party licenses",
                    icon = Icons.Default.Code,
                    onClick = { viewModel.openLicenses() }
                )
            }
        }
    }
    
    // Dialogs
    if (uiState.showApiKeyDialog) {
        ApiKeyDialog(
            currentApiKey = uiState.apiKey,
            onSaveApiKey = { viewModel.saveApiKey(it) },
            onDismiss = { viewModel.hideApiKeyDialog() }
        )
    }
    
    if (uiState.showModelSelector) {
        ModelSelectorDialog(
            currentModel = uiState.settings.model,
            availableModels = uiState.availableModels,
            onModelSelected = { viewModel.updateModel(it) },
            onDismiss = { viewModel.hideModelSelector() }
        )
    }
    
    if (uiState.showSystemPromptDialog) {
        SystemPromptDialog(
            currentPrompt = uiState.settings.systemPrompt,
            onSavePrompt = { viewModel.updateSystemPrompt(it) },
            onDismiss = { viewModel.hideSystemPromptDialog() }
        )
    }
    
    if (uiState.showThemeSelector) {
        ThemeSelectorDialog(
            currentTheme = uiState.themeMode,
            onThemeSelected = { viewModel.updateTheme(it) },
            onDismiss = { viewModel.hideThemeSelector() }
        )
    }
    
    if (uiState.showClearDataDialog) {
        ClearDataDialog(
            onConfirm = { viewModel.clearAllData() },
            onDismiss = { viewModel.hideClearDataDialog() }
        )
    }
}

@Composable
fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Column {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column {
                content()
            }
        }
    }
}

@Composable
fun SettingsItem(
    title: String,
    subtitle: String? = null,
    icon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    onClick: (() -> Unit)? = null,
    isDestructive: Boolean = false
) {
    val colors = if (isDestructive) {
        MaterialTheme.colorScheme.error
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }

    Surface(
        onClick = onClick ?: {},
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surfaceVariant
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            icon?.let {
                Icon(
                    it,
                    contentDescription = null,
                    tint = colors,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(16.dp))
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    color = colors
                )
                subtitle?.let {
                    Text(
                        text = it,
                        fontSize = 14.sp,
                        color = colors.copy(alpha = 0.7f)
                    )
                }
            }

            if (onClick != null) {
                Icon(
                    Icons.Default.ChevronRight,
                    contentDescription = null,
                    tint = colors.copy(alpha = 0.5f)
                )
            }
        }
    }
}

@Composable
fun SettingsSwitch(
    title: String,
    subtitle: String? = null,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surfaceVariant
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                subtitle?.let {
                    Text(
                        text = it,
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }

            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange
            )
        }
    }
}

@Composable
fun SettingsSlider(
    title: String,
    subtitle: String,
    value: Float,
    onValueChange: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float>
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surfaceVariant
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = subtitle,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Slider(
                value = value,
                onValueChange = onValueChange,
                valueRange = valueRange,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}


@tailwind base;
@tailwind components;
@tailwind utilities;

/* Advanced Design System with Modern Theme */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 262 83% 58%;

    /* Advanced Custom Variables */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(285 85% 65%) 25%, hsl(305 87% 72%) 50%, hsl(325 89% 78%) 75%, hsl(345 91% 85%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(220 70% 50%) 0%, hsl(240 75% 60%) 50%, hsl(260 80% 70%) 100%);
    --gradient-surface: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(220 14% 96%) 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-glow: 0 0 40px rgba(147, 51, 234, 0.3);
    --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 262 83% 58%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 262 83% 58%;

    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(285 85% 65%) 25%, hsl(305 87% 72%) 50%, hsl(325 89% 78%) 75%, hsl(345 91% 85%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(220 40% 30%) 0%, hsl(240 45% 35%) 50%, hsl(260 50% 40%) 100%);
    --gradient-surface: linear-gradient(135deg, hsl(222.2 84% 4.9%) 0%, hsl(217.2 32.6% 17.5%) 100%);
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
    --shadow-glow: 0 0 40px rgba(147, 51, 234, 0.5);
    --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    background: var(--gradient-surface);
    min-height: 100vh;
  }

  /* Advanced Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  /* Glass Morphism Effects */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.25);
  }

  /* Advanced Gradients */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  /* Enhanced Shadows */
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  /* Animated Elements */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  /* Custom Scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(147, 51, 234, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(147, 51, 234, 0.5);
  }

  /* Modern Button Hover Effects */
  .btn-modern {
    @apply relative overflow-hidden transition-all duration-300 transform-gpu;
  }

  .btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-modern:hover::before {
    left: 100%;
  }

  .btn-modern:hover {
    @apply scale-105 shadow-glow;
  }

  /* Text Effects */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* Card Enhancements */
  .card-modern {
    @apply relative overflow-hidden transition-all duration-300 hover:shadow-glow;
    background: var(--gradient-surface);
  }

  .card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .glass {
    backdrop-filter: blur(12px);
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.2);
  }
}

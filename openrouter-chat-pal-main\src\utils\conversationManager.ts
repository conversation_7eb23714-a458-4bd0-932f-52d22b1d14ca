
export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  model: string;
  temperature: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isFavorite?: boolean;
  reactions?: string[];
  isEdited?: boolean;
}

export class ConversationManager {
  private static STORAGE_KEY = 'chat_conversations';

  static saveConversation(conversation: Conversation): void {
    const conversations = this.getAllConversations();
    const existingIndex = conversations.findIndex(c => c.id === conversation.id);
    
    if (existingIndex >= 0) {
      conversations[existingIndex] = { ...conversation, updatedAt: new Date() };
    } else {
      conversations.push(conversation);
    }
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(conversations));
  }

  static getAllConversations(): Conversation[] {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (!stored) return [];
    
    return JSON.parse(stored).map((conv: any) => ({
      ...conv,
      createdAt: new Date(conv.createdAt),
      updatedAt: new Date(conv.updatedAt),
      messages: conv.messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    }));
  }

  static deleteConversation(id: string): void {
    const conversations = this.getAllConversations();
    const filtered = conversations.filter(c => c.id !== id);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filtered));
  }

  static exportConversation(conversation: Conversation, format: 'json' | 'txt' | 'md'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(conversation, null, 2);
      case 'txt':
        return conversation.messages.map(msg => 
          `[${msg.timestamp.toLocaleString()}] ${msg.role.toUpperCase()}: ${msg.content}`
        ).join('\n\n');
      case 'md':
        return `# ${conversation.title}\n\n` +
          conversation.messages.map(msg => 
            `## ${msg.role === 'user' ? 'You' : 'AI'} (${msg.timestamp.toLocaleString()})\n\n${msg.content}`
          ).join('\n\n');
      default:
        return '';
    }
  }

  static searchMessages(query: string, conversations: Conversation[]): { conversation: Conversation; message: Message }[] {
    const results: { conversation: Conversation; message: Message }[] = [];
    
    conversations.forEach(conv => {
      conv.messages.forEach(msg => {
        if (msg.content.toLowerCase().includes(query.toLowerCase())) {
          results.push({ conversation: conv, message: msg });
        }
      });
    });
    
    return results;
  }
}

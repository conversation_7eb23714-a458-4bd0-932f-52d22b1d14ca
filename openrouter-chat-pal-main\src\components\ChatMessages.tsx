
import React from 'react';
import ChatMessage from '@/components/ChatMessage';
import { Message } from '@/hooks/useChat';

interface ChatMessagesProps {
  messages: Message[];
  isLoading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  onCopy: (content: string) => void;
  onToggleFavorite: (messageId: string) => void;
  onEditMessage: (messageId: string, newContent: string) => void;
  onRegenerateResponse: (messageId: string) => void;
  onReact: (messageId: string, reaction: string) => void;
}

const ChatMessages: React.FC<ChatMessagesProps> = ({
  messages,
  isLoading,
  messagesEndRef,
  onCopy,
  onToggleFavorite,
  onEditMessage,
  onRegenerateResponse,
  onReact,
}) => {
  return (
    <div className="space-y-4 sm:space-y-8 custom-scrollbar h-full overflow-y-auto">
      {messages.map((message) => (
        <ChatMessage
          key={message.id}
          message={message}
          onCopy={onCopy}
          onToggleFavorite={onToggleFavorite}
          onEditMessage={onEditMessage}
          onRegenerateResponse={onRegenerateResponse}
          onReact={onReact}
        />
      ))}
      {isLoading && (
        <div className="flex justify-start">
          <div className="glass-strong rounded-2xl sm:rounded-3xl rounded-bl-lg sm:rounded-bl-xl px-4 sm:px-6 py-3 sm:py-4 shadow-soft max-w-xs border border-white/20">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="flex gap-1">
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
              <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 font-medium">AI is thinking...</span>
            </div>
          </div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessages;

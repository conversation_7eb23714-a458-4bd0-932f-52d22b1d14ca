
import React from 'react';
import { MessageSquare, <PERSON>, <PERSON>u, <PERSON>ting<PERSON>, Sparkles } from 'lucide-react';
import { ChatSettings } from '@/hooks/useChat';

interface ChatHeaderProps {
  conversationTitle: string;
  settings: ChatSettings;
  onShowSidebar: () => void;
  onShowSettings: () => void;
  onShowApiKeyInput: () => void;
  onClearChat: () => void;
  hasMessages: boolean;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  conversationTitle,
  settings,
  onShowSidebar,
  onShowSettings,
  onShowApiKeyInput,
  onClearChat,
  hasMessages,
}) => {
  return (
    <div className="sticky top-0 z-30 glass-strong border-b border-white/20">
      <div className="max-w-6xl mx-auto px-3 sm:px-6 py-3 sm:py-4 flex items-center justify-between">
        <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
          <button
            onClick={onShowSidebar}
            className="p-2 sm:p-3 text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300 btn-modern"
          >
            <Menu className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
          <div className="flex items-center gap-2 sm:gap-4 min-w-0">
            <div className="w-8 h-8 sm:w-12 sm:h-12 gradient-primary rounded-xl sm:rounded-2xl flex items-center justify-center shadow-glow animate-glow flex-shrink-0">
              <MessageSquare className="w-4 h-4 sm:w-6 sm:h-6 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-2xl font-bold text-gradient truncate">
                {conversationTitle}
              </h1>
              <div className="flex items-center gap-1 sm:gap-2">
                <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-purple-500 flex-shrink-0" />
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">
                  Powered by {settings.model}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-1 sm:gap-3 flex-shrink-0">
          <button
            onClick={onShowSettings}
            className="p-2 sm:p-3 text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300 btn-modern"
            title="Advanced Settings"
          >
            <Settings className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
          <button
            onClick={onShowApiKeyInput}
            className="p-2 sm:p-3 text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300 btn-modern"
            title="Manage API Key"
          >
            <Key className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
          {hasMessages && (
            <button
              onClick={onClearChat}
              className="hidden sm:block px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300 btn-modern"
            >
              Clear Chat
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;

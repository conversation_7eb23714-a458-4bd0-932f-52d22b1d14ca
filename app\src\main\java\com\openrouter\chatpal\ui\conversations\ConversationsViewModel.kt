package com.openrouter.chatpal.ui.conversations

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.openrouter.chatpal.data.model.Conversation
import com.openrouter.chatpal.data.repository.ChatRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ConversationsViewModel @Inject constructor(
    private val chatRepository: ChatRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ConversationsUiState())
    val uiState: StateFlow<ConversationsUiState> = _uiState.asStateFlow()
    
    init {
        loadConversations()
    }
    
    private fun loadConversations() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            chatRepository.getAllConversations().collect { conversations ->
                _uiState.update { 
                    it.copy(
                        conversations = conversations,
                        isLoading = false
                    )
                }
            }
        }
    }
    
    fun toggleFavorite(conversationId: String) {
        viewModelScope.launch {
            val conversation = _uiState.value.conversations.find { it.id == conversationId }
            conversation?.let {
                val updatedConversation = it.copy(isFavorite = !it.isFavorite)
                chatRepository.updateConversation(updatedConversation)
            }
        }
    }
    
    fun deleteConversation(conversationId: String) {
        viewModelScope.launch {
            chatRepository.deleteConversation(conversationId)
        }
    }
    
    fun exportConversation(conversationId: String, format: String) {
        viewModelScope.launch {
            try {
                val exportData = chatRepository.exportConversation(conversationId, format)
                _uiState.update { 
                    it.copy(exportData = exportData, exportFormat = format)
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = "Failed to export conversation: ${e.message}")
                }
            }
        }
    }
    
    fun searchConversations(query: String) {
        viewModelScope.launch {
            if (query.isBlank()) {
                loadConversations()
            } else {
                _uiState.update { it.copy(isLoading = true) }
                
                val searchResults = chatRepository.searchConversations(query)
                _uiState.update { 
                    it.copy(
                        conversations = searchResults,
                        isLoading = false
                    )
                }
            }
        }
    }
    
    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
    
    fun clearExportData() {
        _uiState.update { it.copy(exportData = null, exportFormat = null) }
    }
}

data class ConversationsUiState(
    val conversations: List<Conversation> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val exportData: String? = null,
    val exportFormat: String? = null
)

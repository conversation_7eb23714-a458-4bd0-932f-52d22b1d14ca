
interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface OpenRouterSettings {
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export const sendMessage = async (
  apiKey: string, 
  messages: Message[], 
  settings: OpenRouterSettings = {}
): Promise<string> => {
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bear<PERSON> ${apiKey}`,
      'HTTP-Referer': window.location.origin,
      'X-Title': 'Advanced AI Chat App',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: settings.model || 'openai/gpt-4.1',
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })),
      temperature: settings.temperature ?? 0.7,
      max_tokens: settings.max_tokens ?? 2000,
      stream: false,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  
  if (!data.choices || !data.choices[0] || !data.choices[0].message) {
    throw new Error('Invalid response format from OpenRouter API');
  }

  return data.choices[0].message.content;
};

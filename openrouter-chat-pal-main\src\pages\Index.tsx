
import React from 'react';
import ChatHeader from '@/components/ChatHeader';
import ChatWelcome from '@/components/ChatWelcome';
import ChatMessages from '@/components/ChatMessages';
import MessageInput from '@/components/MessageInput';
import ApiKeyInput from '@/components/ApiKeyInput';
import ConversationSidebar from '@/components/ConversationSidebar';
import AdvancedSettings from '@/components/AdvancedSettings';
import { useChat } from '@/hooks/useChat';
import { useChatHandlers } from '@/hooks/useChatHandlers';

const Index = () => {
  const {
    messages,
    setMessages,
    apiKey,
    setApiKey,
    isLoading,
    setIsLoading,
    showApiKeyInput,
    setShowApiKeyInput,
    showSidebar,
    setShowSidebar,
    showSettings,
    setShowSettings,
    currentConversationId,
    setCurrentConversationId,
    conversationTitle,
    setConversationTitle,
    settings,
    setSettings,
    messagesEndRef,
    toast,
    saveCurrentConversation,
    handleSelectConversation,
    handleNewConversation,
    clearChat,
  } = useChat();

  const {
    handleSendMessage,
    handleApiKeySubmit,
    copyMessage,
    handleToggleFavorite,
    handleEditMessage,
    handleRegenerateResponse,
    handleReact,
  } = useChatHandlers({
    apiKey,
    setApiKey,
    messages,
    setMessages,
    setIsLoading,
    setShowApiKeyInput,
    conversationTitle,
    setConversationTitle,
    currentConversationId,
    setCurrentConversationId,
    settings,
    saveCurrentConversation,
    toast,
  });

  return (
    <div className="min-h-screen flex relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 dark:from-purple-900/20 dark:via-blue-900/20 dark:to-indigo-900/20"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-300/30 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-blue-300/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-indigo-300/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      {/* Mobile Overlay for Sidebar */}
      {showSidebar && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Sidebar */}
      <ConversationSidebar
        isOpen={showSidebar}
        currentConversationId={currentConversationId}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
        onClose={() => setShowSidebar(false)}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col relative min-w-0">
        {/* Header */}
        <ChatHeader
          conversationTitle={conversationTitle}
          settings={settings}
          onShowSidebar={() => setShowSidebar(true)}
          onShowSettings={() => setShowSettings(true)}
          onShowApiKeyInput={() => setShowApiKeyInput(true)}
          onClearChat={clearChat}
          hasMessages={messages.length > 0}
        />

        {/* Chat Content */}
        <div className="flex-1 w-full overflow-hidden">
          <div className="h-full max-w-6xl mx-auto px-3 sm:px-6 py-4 sm:py-8 pb-24 sm:pb-32">
            {messages.length === 0 ? (
              <ChatWelcome
                hasApiKey={!!apiKey}
                onShowApiKeyInput={() => setShowApiKeyInput(true)}
              />
            ) : (
              <ChatMessages
                messages={messages}
                isLoading={isLoading}
                messagesEndRef={messagesEndRef}
                onCopy={copyMessage}
                onToggleFavorite={handleToggleFavorite}
                onEditMessage={handleEditMessage}
                onRegenerateResponse={handleRegenerateResponse}
                onReact={handleReact}
              />
            )}
          </div>
        </div>

        {/* Message Input */}
        <MessageInput onSendMessage={handleSendMessage} disabled={isLoading || !apiKey} />
      </div>

      {/* Modals */}
      {showApiKeyInput && (
        <ApiKeyInput
          onSubmit={handleApiKeySubmit}
          onClose={() => setShowApiKeyInput(false)}
          currentApiKey={apiKey}
        />
      )}

      <AdvancedSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        settings={settings}
        onSettingsChange={setSettings}
      />
    </div>
  );
};

export default Index;

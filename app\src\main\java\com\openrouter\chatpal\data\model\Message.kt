package com.openrouter.chatpal.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.util.Date

@Entity(tableName = "messages")
@Parcelize
data class Message(
    @PrimaryKey
    val id: String,
    val conversationId: String,
    val role: MessageRole,
    val content: String,
    val timestamp: Date,
    val isFavorite: Boolean = false,
    val reactions: List<String> = emptyList(),
    val isEdited: Boolean = false
) : Parcelable

enum class MessageRole {
    USER, ASSISTANT, SYSTEM
}

@Entity(tableName = "conversations")
@Parcelize
data class Conversation(
    @PrimaryKey
    val id: String,
    val title: String,
    val createdAt: Date,
    val updatedAt: Date,
    val isFavorite: Boolean = false,
    val messageCount: Int = 0
) : Parcelable

data class ChatSettings(
    val model: String = "openai/gpt-4.1",
    val temperature: Float = 0.7f,
    val maxTokens: Int = 2000,
    val systemPrompt: String = "You are a helpful assistant."
)

data class OpenRouterRequest(
    val model: String,
    val messages: List<OpenRouterMessage>,
    val temperature: Float,
    val max_tokens: Int,
    val stream: Boolean = false
)

data class OpenRouterMessage(
    val role: String,
    val content: String
)

data class OpenRouterResponse(
    val choices: List<Choice>
)

data class Choice(
    val message: OpenRouterMessage
)

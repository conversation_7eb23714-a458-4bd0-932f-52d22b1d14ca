package com.openrouter.chatpal.data.database

import androidx.room.*
import com.openrouter.chatpal.data.model.Conversation
import kotlinx.coroutines.flow.Flow

@Dao
interface ConversationDao {
    
    @Query("SELECT * FROM conversations ORDER BY updatedAt DESC")
    fun getAllConversations(): Flow<List<Conversation>>
    
    @Query("SELECT * FROM conversations WHERE isFavorite = 1 ORDER BY updatedAt DESC")
    fun getFavoriteConversations(): Flow<List<Conversation>>
    
    @Query("SELECT * FROM conversations WHERE title LIKE '%' || :query || '%' ORDER BY updatedAt DESC")
    suspend fun searchConversations(query: String): List<Conversation>
    
    @Query("SELECT * FROM conversations WHERE id = :id")
    suspend fun getConversationById(id: String): Conversation?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConversation(conversation: Conversation)
    
    @Update
    suspend fun updateConversation(conversation: Conversation)
    
    @Delete
    suspend fun deleteConversation(conversation: Conversation)
    
    @Query("DELETE FROM conversations WHERE id = :id")
    suspend fun deleteConversationById(id: String)
    
    @Query("DELETE FROM conversations")
    suspend fun deleteAllConversations()
    
    @Query("UPDATE conversations SET isFavorite = :isFavorite WHERE id = :conversationId")
    suspend fun updateConversationFavorite(conversationId: String, isFavorite: Boolean)
    
    @Query("UPDATE conversations SET title = :title WHERE id = :conversationId")
    suspend fun updateConversationTitle(conversationId: String, title: String)
    
    @Query("UPDATE conversations SET messageCount = :count WHERE id = :conversationId")
    suspend fun updateMessageCount(conversationId: String, count: Int)
}

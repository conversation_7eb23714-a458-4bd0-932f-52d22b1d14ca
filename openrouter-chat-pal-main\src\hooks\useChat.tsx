
import { useState, useRef, useEffect } from 'react';
import { ConversationManager, Conversation } from '@/utils/conversationManager';
import { useToast } from '@/hooks/use-toast';

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isFavorite?: boolean;
  reactions?: string[];
  isEdited?: boolean;
}

export interface ChatSettings {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
}

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [apiKey, setApiKey] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [conversationTitle, setConversationTitle] = useState('New Conversation');
  
  const [settings, setSettings] = useState<ChatSettings>({
    model: 'openai/gpt-4.1',
    temperature: 0.7,
    maxTokens: 2000,
    systemPrompt: 'You are a helpful assistant.'
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Load API key from localStorage on component mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('openrouter_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    } else {
      setShowApiKeyInput(true);
    }
  }, []);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const saveCurrentConversation = () => {
    if (messages.length === 0) return;
    
    const conversation: Conversation = {
      id: currentConversationId || Date.now().toString(),
      title: conversationTitle,
      messages,
      createdAt: currentConversationId ? new Date() : new Date(),
      updatedAt: new Date(),
      model: settings.model,
      temperature: settings.temperature
    };
    
    ConversationManager.saveConversation(conversation);
    if (!currentConversationId) {
      setCurrentConversationId(conversation.id);
    }
  };

  const handleSelectConversation = (conversation: Conversation) => {
    setMessages(conversation.messages);
    setCurrentConversationId(conversation.id);
    setConversationTitle(conversation.title);
    setShowSidebar(false);
  };

  const handleNewConversation = () => {
    setMessages([]);
    setCurrentConversationId(null);
    setConversationTitle('New Conversation');
    setShowSidebar(false);
  };

  const clearChat = () => {
    setMessages([]);
    setCurrentConversationId(null);
    setConversationTitle('New Conversation');
    toast({
      title: "Chat Cleared",
      description: "All messages have been cleared.",
    });
  };

  return {
    // State
    messages,
    setMessages,
    apiKey,
    setApiKey,
    isLoading,
    setIsLoading,
    showApiKeyInput,
    setShowApiKeyInput,
    showSidebar,
    setShowSidebar,
    showSettings,
    setShowSettings,
    currentConversationId,
    setCurrentConversationId,
    conversationTitle,
    setConversationTitle,
    settings,
    setSettings,
    messagesEndRef,
    toast,
    // Functions
    saveCurrentConversation,
    handleSelectConversation,
    handleNewConversation,
    clearChat,
  };
};

package com.openrouter.chatpal.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.gson.Gson
import com.openrouter.chatpal.data.model.ChatSettings
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PreferencesManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedPrefs: SharedPreferences = EncryptedSharedPreferences.create(
        context,
        "secure_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    private val regularPrefs: SharedPreferences = context.getSharedPreferences(
        "app_prefs", 
        Context.MODE_PRIVATE
    )
    
    private val gson = Gson()
    
    companion object {
        private const val KEY_API_KEY = "api_key"
        private const val KEY_CHAT_SETTINGS = "chat_settings"
        private const val KEY_THEME_MODE = "theme_mode"
        private const val KEY_FIRST_LAUNCH = "first_launch"
    }
    
    // API Key (encrypted)
    fun saveApiKey(apiKey: String) {
        encryptedPrefs.edit()
            .putString(KEY_API_KEY, apiKey)
            .apply()
    }
    
    fun getApiKey(): String {
        return encryptedPrefs.getString(KEY_API_KEY, "") ?: ""
    }
    
    fun clearApiKey() {
        encryptedPrefs.edit()
            .remove(KEY_API_KEY)
            .apply()
    }
    
    // Chat Settings
    fun saveChatSettings(settings: ChatSettings) {
        val json = gson.toJson(settings)
        regularPrefs.edit()
            .putString(KEY_CHAT_SETTINGS, json)
            .apply()
    }
    
    fun getChatSettings(): ChatSettings {
        val json = regularPrefs.getString(KEY_CHAT_SETTINGS, null)
        return if (json != null) {
            try {
                gson.fromJson(json, ChatSettings::class.java)
            } catch (e: Exception) {
                ChatSettings() // Return default if parsing fails
            }
        } else {
            ChatSettings() // Return default if no settings saved
        }
    }
    
    // Theme Mode
    fun saveThemeMode(mode: String) {
        regularPrefs.edit()
            .putString(KEY_THEME_MODE, mode)
            .apply()
    }
    
    fun getThemeMode(): String {
        return regularPrefs.getString(KEY_THEME_MODE, "system") ?: "system"
    }
    
    // First Launch
    fun isFirstLaunch(): Boolean {
        return regularPrefs.getBoolean(KEY_FIRST_LAUNCH, true)
    }
    
    fun setFirstLaunchCompleted() {
        regularPrefs.edit()
            .putBoolean(KEY_FIRST_LAUNCH, false)
            .apply()
    }
    
    // Clear all data
    fun clearAllData() {
        encryptedPrefs.edit().clear().apply()
        regularPrefs.edit().clear().apply()
    }
}
